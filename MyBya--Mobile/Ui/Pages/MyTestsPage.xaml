<?xml version="1.0" encoding="utf-8" ?>
<pages:BaseContentPage
    x:Class="MyBya.Ui.Pages.MyTestsPage"
    x:TypeArguments="viewmodels:MyTestsViewModel"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:pages="clr-namespace:MyBya.Ui.Pages.Common"
    xmlns:viewmodels="clr-namespace:MyBya.Ui.ViewModels"
    Title="My Tests">

    <Grid RowDefinitions="Auto,*">
        <!-- Header -->
        <Grid Row="0" ColumnDefinitions="50,*" Margin="20,40,20,0">
            <Frame Grid.Column="0"
                BackgroundColor="Transparent"
                BorderColor="Transparent"
                WidthRequest="40"
                HeightRequest="40"
                CornerRadius="0"
                HasShadow="False"
                Padding="0"
                Margin="0">
                <ImageButton
                    Command="{Binding BackCommand}"
                    Source="arrow_left.png"
                    BackgroundColor="Transparent"
                    WidthRequest="40"
                    HeightRequest="40"/>
            </Frame>
            <Label Grid.Column="1"
                Text="My Tests"
                FontSize="28"
                FontAttributes="Bold"
                TextColor="#FF7FE8"
                VerticalOptions="Center"
                HorizontalOptions="Center" />
        </Grid>

        <!-- Content -->
        <ScrollView Grid.Row="1" Margin="20,30,20,20">
            <StackLayout Spacing="20">
                <!-- Description Text -->
                <Label Text="The following tests are active for your account."
                       FontSize="16"
                       TextColor="White"
                       LineHeight="1.3" />

                <Label Text="You can retake each test every 30 days."
                       FontSize="16"
                       TextColor="White"
                       Margin="0,0,0,10" />

                <!-- Tests List -->
                <CollectionView ItemsSource="{Binding Tests}"
                               VerticalOptions="FillAndExpand">
                    <CollectionView.ItemTemplate>
                        <DataTemplate>
                            <Grid Margin="0,0,0,15">
                                <Frame BackgroundColor="White"
                                       CornerRadius="10"
                                       HasShadow="False"
                                       Padding="20">
                                    <StackLayout Spacing="10">
                                        <!-- Test Name -->
                                        <Label Text="{Binding Name}"
                                               FontSize="20"
                                               FontAttributes="Bold"
                                               TextColor="#FF7FE8" />

                                        <!-- Test Date Section (only show if has test date) -->
                                        <StackLayout IsVisible="{Binding TestDate, Converter={StaticResource StringToBoolConverter}}">
                                            <Label Text="Test Date"
                                                   FontSize="14"
                                                   TextColor="Black"
                                                   FontAttributes="Bold" />
                                            <Label Text="{Binding TestDate}"
                                                   FontSize="18"
                                                   TextColor="Black" />

                                            <!-- Retake Date -->
                                            <Label Text="{Binding CanRetakeDate, StringFormat='TEST CAN BE RETAKEN {0}'}"
                                                   FontSize="12"
                                                   TextColor="Gray"
                                                   BackgroundColor="#F0F0F0"
                                                   Padding="10,8"
                                                   HorizontalTextAlignment="Center"
                                                   Margin="0,10,0,0" />
                                        </StackLayout>

                                        <!-- Take Test Button (only show if can take test) -->
                                        <Button Text="TAKE TEST"
                                                Command="{Binding Source={RelativeSource AncestorType={x:Type viewmodels:MyTestsViewModel}}, Path=TakeTestCommand}"
                                                CommandParameter="{Binding .}"
                                                IsVisible="{Binding CanTakeTest}"
                                                BackgroundColor="Transparent"
                                                TextColor="Black"
                                                BorderColor="Black"
                                                BorderWidth="2"
                                                CornerRadius="5"
                                                FontSize="14"
                                                FontAttributes="Bold"
                                                Margin="0,10,0,0" />
                                    </StackLayout>
                                </Frame>
                            </Grid>
                        </DataTemplate>
                    </CollectionView.ItemTemplate>
                </CollectionView>
            </StackLayout>
        </ScrollView>
    </Grid>
</pages:BaseContentPage>
